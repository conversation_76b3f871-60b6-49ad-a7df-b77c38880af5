// DynamicContainerTemplateComponent
import {
    AfterViewInit,
    Component,
    ComponentRef,
    computed,
    EventEmitter,
    input,
    Input,
    OnDestroy,
    OnInit,
    output,
    Output,
    Signal,
    ViewChild
} from '@angular/core';
import {SuggestAttribute} from '../../smart-creation/models/smart-creation-validation.types';
import {ViewModeEnum} from '../models/material-editor.types';
import {FieldType} from '@creactives/models';
import {SmartCreationFormControl} from '../../smart-creation/models/smart-creation-form.types';
import {
    DynamicInputComponentProps
} from 'src/app/modules/materials-editor/dynamic-components/dynamic-components.models';
import {Tam4ComponentEvent} from 'src/app/models';
import {of, Subject} from 'rxjs';
import {debounceTime, take, takeUntil} from 'rxjs/operators';
import {DynamicFormEventPayload} from 'src/app/modules/materials-modal/store/materials-modal.action-types';
import {NgComponentOutlet} from "@angular/common";

const fieldWithSuggestion = [
    FieldType.textfield,
    FieldType.dropdown,
    FieldType.autocomplete,
    FieldType.date,
    FieldType.description_input,
    FieldType.textarea,
    FieldType.warehouse,
    FieldType.textfieldNumeric
];

@Component({
    selector: '[scDynamicContainerTemplate]',
    template: `
        <div tamFormInput
             class="small-form-control"
             [hasError]="dynamicParams()?.hasError"
             [mandatory]="mandatory()"
             [multiple]="dynamicParams()?.componentProps?.multiple"
             [coreAttribute]="coreAttribute"
             [isViewOnlyData]="isViewOnlyData()"
             [label]="label"
             [control]="formControl()"
             [fieldName]="dynamicParams()?.formControlName"
             [inputType]="dynamicParams()?.type"
             [viewMode]="viewMode()"
             [grAttributes]="dynamicParams()?.goldenRecordAttribute"
             [showEmptyValues]="showEmptyValues"
             [currentClient]="currentClient"
             [locale]="locale()"
             [digits]="dynamicParams()?.componentProps?.decimals"
             [ngClass]="{'highlight-mismatched-config': enableDevDiagnostic && dynamicParams()?.customerFieldInvalidForClientConfig}"
             [edited]="dynamicParams()?.componentProps?.edited"
             [disableCopy]="disableCopy()"
        >
            <ng-container *ngComponentOutlet="dynamicComponent;inputs: enhancedDynamicsParams()"/>
            <dynamic-input-suggestion-attribute *ngIf="isSuggestionsVisible()" [suggestionAttributes]="suggestions"
                                                (onCopyValue)="copyValue($event)"></dynamic-input-suggestion-attribute>
        </div>
    `,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class]': "'dynamic-formcontrol-container'"
    },
    styleUrls: ['./dynamic-container-template.component.scss']
})
export class DynamicContainerTemplateComponent implements OnInit, OnDestroy, AfterViewInit {

    @Input() dynamicComponent: any;
    dynamicParams: Signal<DynamicInputComponentProps> = input(null);
    @Input() label: string;
    mandatory: Signal<boolean> = input(false);
    @Input() coreAttribute: boolean;
    @Input() suggestions: SuggestAttribute[];
    editable: Signal<boolean> = input(false);
    viewMode: Signal<ViewModeEnum> = input(null);
    @Input() showEmptyValues = true;
    @Input() currentClient: string;
    @Input() enableDevDiagnostic: boolean = false;

    locale: Signal<string> = input<string>();

    disableCopy = input<boolean>(false);

    @Output() formComponentEvent: EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>> = new EventEmitter<Tam4ComponentEvent<any, DynamicFormEventPayload>>();
    onFocusInEvent = output<number>();

    @ViewChild(NgComponentOutlet) componentOutlet!: NgComponentOutlet;
    private componentRef: ComponentRef<any> | null = null;

    ngOnDestroy$: Subject<void> = new Subject<void>();

    formControl = computed(() => {
        const dynamicParams = this.dynamicParams();
        return dynamicParams?.formGroup?.controls?.[dynamicParams?.formControlName] as SmartCreationFormControl;
    });

    isViewOnlyFunction = computed(() => {
        const viewMode = this.viewMode();
        return viewMode === ViewModeEnum.SUMMARY || viewMode === ViewModeEnum.DETAILS || viewMode === ViewModeEnum.PROCESS_DETAILS_VIEW || viewMode === ViewModeEnum.PROCESS_DETAILS_EDIT || viewMode === ViewModeEnum.GR_EDIT_DETAILS;
    });

    isSpecialComponent = computed(() => {
        const type = this.dynamicParams()?.type;

        return type === FieldType.alternativeUnitsOfMeasure
            || type === FieldType.plantData
            || type === FieldType.storageLocations
            || type === FieldType.relationship
            || type === FieldType.warehouse
            || type === FieldType.materialValuations;
    });

    isViewOnlyData = computed(() => {
        return this.isSpecialComponent() ? false : (this.isViewOnlyFunction() || !this.editable());
    });

    enhancedDynamicsParams = computed(() => {
        return {
            ...this.dynamicParams(),
            currentClient: this.currentClient
        };
    });

    ngOnInit(): void {
        if (this.dynamicParams()?.formComponentEvent) {
            this.dynamicParams().formComponentEvent
                .pipe(takeUntil(this.ngOnDestroy$))
                .subscribe(e => this.formComponentEvent.emit(e));
        }
    }

    ngOnDestroy() {
        this.ngOnDestroy$.next();
        this.ngOnDestroy$.complete();
        // Clean up
        if (this.componentRef) {
            this.componentRef = null;
        }
    }

    isSuggestionsVisible(): boolean {
        return this.editable()
            && (this.viewMode() === ViewModeEnum.GR_EDIT || this.viewMode() === ViewModeEnum.GR_APPROVE)
            && this.dynamicParams()
            && fieldWithSuggestion.includes(this.dynamicParams()?.type);
    }

    copyValue(value) {
        const dynamicParams = this.dynamicParams();

        if (dynamicParams.formGroup && dynamicParams.onInputValueEvent) {
            this.copyValueToForm(value);
            dynamicParams.onInputValueEvent(dynamicParams.id,
                dynamicParams.formGroup,
                dynamicParams?.sheetIndex);
        }
    }

    copyValueToForm(copiedValue: string) {
        this.dynamicParams()?.formGroup?.controls?.[this.dynamicParams().id].setValue(copiedValue);
    }

    ngAfterViewInit() {
        // Wait for next tick to ensure the component is loaded
        of("registerMe").pipe(takeUntil(this.ngOnDestroy$), debounceTime(10), take(1)).subscribe(() => {
            this.setupComponentListener();
        });
    }

    private setupComponentListener(): void {
        // Get the component instance
        const componentRef = (this.componentOutlet as any)._componentRef;
        if (componentRef) {
            this.componentRef = componentRef;
            const instance = componentRef.instance;
            const componentName = instance?.constructor?.name || 'unknown';
            const componentInsName = instance?.id || 'unknown';

            // Subscribe to the onFocusInEvent if it exists
            if (instance.onFocusInEvent) {
                instance.onFocusInEvent.subscribe((event: any) => {
                    // Handle the focus event here
                    // console.log('Focus event received on :', componentName, componentInsName, event);
                    this.onFocusInEvent.emit(event);
                });
            }
        }
    }

}
