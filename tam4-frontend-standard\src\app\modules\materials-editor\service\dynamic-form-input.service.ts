import {SuggestGrAttributes} from '../../smart-creation/models/smart-creation-validation.types';
import {Injectable} from '@angular/core';
import {FormGroup, Validators} from '@angular/forms';
import {FieldType} from '@creactives/models';

import {AlternativeUnitsOfMeasure} from 'src/app/modules/smart-creation/models/smart-creation-validation.types';

import {
    SmartCreationMaterialDetail,
    SmartFieldConfiguration,
    SmartPlantData
} from 'src/app/modules/smart-creation/models/smart-creation.types';
import {SmartCreationFormControl} from '../../smart-creation/models/smart-creation-form.types';

import {
    ATTRIBUTEVALUETYPE,
    DocumentData,
    DynamicComponentWrapper,
    DynamicGroupInputTableComponent,
    DynamicInputComponentProps
} from '../dynamic-components';
import {
    BaseMaterialEditorTabsKeys,
    DynamicFormGroup,
    Relationship,
    ValidateDataResponse,
    ViewModeEnum
} from '../models/material-editor.types';
import {TranslateService} from '@ngx-translate/core';
import {Observable} from 'rxjs';
import {FormUtils, ObjectsUtils} from 'src/app/utils';
import {buildDescription, setUseTranslatePipe} from 'src/app/modules/materials-editor/common/materials-editor.function';
import {DynamicFormInputFactory} from 'src/app/modules/materials-editor/service/dynamic-form-input.factory';
import {needFlatArray} from 'src/app/modules/smart-creation/commons/smart-creation.constants';
import {MessageSeverityType, TAm4StandardFieldNames} from 'src/app/models';
import {AttributeNamesTranslationService} from "@creactives/tam4-translation-core";

@Injectable({
    providedIn: 'root'
})
export class DynamicFormInputService {
    private static buildNormalizedDescription = buildDescription;

    constructor(private dynamicFormInputFactoryService: DynamicFormInputFactory, private attrNameTranslate: AttributeNamesTranslationService) {
    }

    public initDescriptionsFormGroup(sheets: SmartFieldConfiguration[],
                                     page: string,
                                     viewMode: ViewModeEnum,
                                     sheetIndex: number,
                                     formGroup?: FormGroup,
                                     inputChange?: (source: string, value: string) => void,
                                     suggestAttributes?: SuggestGrAttributes) {

        const items = [];
        const descriptionControls = sheets.filter(v => v.attributeValueType === ATTRIBUTEVALUETYPE.DESCRIPTION);
        const suggestionControls = sheets.filter(v => v.attributeValueType === ATTRIBUTEVALUETYPE.NORMALIZEDESCRIPTION);

        if (formGroup) {
            formGroup?.enable({onlySelf: true, emitEvent: false});

            descriptionControls.forEach((originalCfg: SmartFieldConfiguration) => {
                const smartFieldConfigurationNew = ObjectsUtils.deepClone(originalCfg);
                const control = this.initFormControl(smartFieldConfigurationNew);
                this.setFormControlStatus(viewMode, smartFieldConfigurationNew, control);
                formGroup.addControl(smartFieldConfigurationNew.id,
                    control
                );

                smartFieldConfigurationNew.type = originalCfg?.type === FieldType.textarea ? FieldType.description_textarea : FieldType.description_input;
                smartFieldConfigurationNew.textarea = originalCfg?.type === FieldType.textarea;
                const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(smartFieldConfigurationNew,
                    page,
                    formGroup,
                    sheetIndex,
                    originalCfg.order,
                    null,
                    null,
                    null,
                    viewMode);
                const suggestionControlIndex = suggestionControls.findIndex(x => smartFieldConfigurationNew.id.startsWith(x.relatedAttribute));

                let componentParamsUpdated: DynamicInputComponentProps = {
                    ...dynamicComponentWrapper.componentParams,
                    componentProps: {
                        ...dynamicComponentWrapper.componentParams.componentProps,
                        value: smartFieldConfigurationNew.value,
                        onDescriptionInputChange: (source, value) => inputChange && inputChange(source, value)
                    }
                };
                // poi se trovo che c'è anche la relativa normalizzata visibile, gli aggiungo anche il riferimento suggestionsAttribute
                if (suggestionControlIndex >= 0) {
                    componentParamsUpdated = {
                        ...componentParamsUpdated,
                        componentProps: {
                            ...componentParamsUpdated.componentProps,
                            actionType: 'input',
                            suggestedValue: suggestionControls[suggestionControlIndex]?.value,
                            suggestionsAttribute: this.getSuggestAttributes(suggestAttributes, smartFieldConfigurationNew.id),
                        },
                    };
                }
                dynamicComponentWrapper.componentParams = componentParamsUpdated;
                items.push({
                    ...dynamicComponentWrapper,
                    ...smartFieldConfigurationNew
                });

            });
        }
        return {
            formGroup,
            items
        };
    }

    public initFormGroup(
        sheets: SmartFieldConfiguration[],
        page: string,
        viewMode: ViewModeEnum,
        sheetIndex: number,
        formGroup?: FormGroup,
        dynamicAutocompleteFn?: (source: string, id: string, documentData: DocumentData) => Observable<any>,
        initialData?: SmartCreationMaterialDetail,
        updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void) {
        const items = [];
        sheets?.forEach((smartFieldConfiguration: SmartFieldConfiguration) => {
            formGroup?.enable({onlySelf: true, emitEvent: false});
            if (ObjectsUtils.isNoU(formGroup?.controls?.[smartFieldConfiguration.id])) {

                const control: SmartCreationFormControl = this.initFormControl(smartFieldConfiguration);
                this.setFormControlStatus(viewMode, smartFieldConfiguration, control);

                formGroup?.addControl(smartFieldConfiguration.id,
                    control
                );

                if (smartFieldConfiguration.unitsOfMeasure && smartFieldConfiguration.unitsOfMeasure.length > 0) {
                    const uomControl: SmartCreationFormControl = new SmartCreationFormControl(smartFieldConfiguration.unitsOfMeasureSelected ?? smartFieldConfiguration.unitsOfMeasure[0],
                        [],
                        null,
                        null,
                        smartFieldConfiguration.unitsOfMeasure,
                        null
                    );
                    this.setFormControlStatus(viewMode, smartFieldConfiguration, uomControl);
                    formGroup?.addControl(smartFieldConfiguration.id + '.mu',
                        uomControl
                    );
                }
            } else if (ObjectsUtils.isNotNoU(formGroup?.controls?.[smartFieldConfiguration.id])) {
                this.patchFormControl(formGroup, smartFieldConfiguration, viewMode);
            }

            if (ObjectsUtils.isNoU(formGroup)) {
                smartFieldConfiguration.type = FieldType.description_input;
            } else {
                // Removing form formGroup not valid controls
                Object.keys(formGroup?.controls).forEach(key => {

                    // if (key.endsWith(".mu")) {
                    const _originalCrtlName = key.replace(".mu", "");
                    const _originalCrtl = sheets.find(k => k.id === _originalCrtlName);

                    if (ObjectsUtils.isNoU(_originalCrtl)) {
                        formGroup.removeControl(key);
                    }
                    // }
                    // else {
                    //   if (!sheets.some(sfc => sfc.id === key)) {
                    //     formGroup.removeControl(key);
                    //   }
                    // }
                })
            }
            // adding the name of the pipe to load de description (if needed)
            smartFieldConfiguration.useTranslatePipe = setUseTranslatePipe(smartFieldConfiguration.id);

            const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(smartFieldConfiguration,
                page,
                formGroup,
                sheetIndex,
                smartFieldConfiguration.order,
                dynamicAutocompleteFn,
                initialData,
                updateAlternativeUomFn,
                viewMode);
            console.log("initFormGroup", dynamicComponentWrapper);

            items.push({
                ...dynamicComponentWrapper,
                ...smartFieldConfiguration
            });
        });

        return {
            formGroup,
            items
        };
    }

    getSuggestAttributes(suggestAttributes: SuggestGrAttributes, id: string) {
        return id && suggestAttributes && suggestAttributes[id] ? suggestAttributes[id].filter(item => item.value !== '') : null;
    }

    public initRelationshipFormGroup(relationships: Relationship[],
                                     formGroup?: FormGroup,
                                     goldenRecord?: string,
                                     goldenRecordCode?: string): {
        formGroup: FormGroup, items: any[]
    } {
        return {
            formGroup,
            items: [
                {
                    component: DynamicGroupInputTableComponent,
                    componentParams: {
                        componentProps: {
                            relationships,
                            goldenRecord,
                            goldenRecordCode
                        }
                    }
                }
            ]
        };
    }

    validateAllForms(dynamicFormGroup: DynamicFormGroup[], translate: TranslateService, plantValidation: 'skip' | 'only' | 'clear' | null) {
        if (ObjectsUtils?.isNoU(dynamicFormGroup)) {
            return true;
        }

        let isValid = true;
        dynamicFormGroup?.forEach(f => {
            if (plantValidation === 'clear') {
                if (f.key === BaseMaterialEditorTabsKeys.PLANT_DATA) {
                    FormUtils.markAllAsPristine(f.formGroup);
                    f.errors = [];
                }
                return;
            }

            if (f.key === BaseMaterialEditorTabsKeys.PLANT_DATA && plantValidation === 'skip') {
                FormUtils.markAllAsPristine(f.formGroup);
                f.errors = [];
                return;
            }

            if (f.key !== BaseMaterialEditorTabsKeys.PLANT_DATA && plantValidation === 'only') {
                FormUtils.markAllAsPristine(f.formGroup);
                f.errors = [];
                return;
            }
            f.errors = [];
            f.formGroup?.markAllAsTouched();

            Object.entries(f.formGroup.controls).forEach(([ctrlName, _formControl]) => {
                const formControl = ObjectsUtils.forceCast<SmartCreationFormControl>(_formControl);
                if (formControl.disabled) {
                    formControl.markAsPristine();
                } else if (formControl?.invalid === true) {
                    formControl.validateErrors = null;
                    f.errors.push({
                            field: ctrlName,
                            label: translate?.instant('smartCreation.smartValidation.mandatory-error', {field: this.attrNameTranslate.translate(ctrlName)}),
                            statusMessage: translate?.instant('smartCreation.smartValidation.mandatory-error', {field: this.attrNameTranslate.translate(ctrlName)})
                        }
                    );
                    isValid = false;
                } else {
                    formControl.validateErrors = null;
                }
                formControl.updateValueAndValidity();
            });

            if (isValid) {
                f.formGroup.setErrors(null);
                f.formGroup?.updateValueAndValidity();
            }
        });

        switch (plantValidation) {
            case "clear":
                return true;
            default:
                return isValid;
        }
    }

    setValidateErrorsOnCreation(validateInitialDataResult: ValidateDataResponse, dynamicFormGroup: DynamicFormGroup[]) {
        let hasWarnings = false;
        let hasErrors = false;

        dynamicFormGroup?.forEach(f => {
            f.errors = [];

            if (f.key === BaseMaterialEditorTabsKeys.PLANT_DATA) {
                const plantValidation = validateInitialDataResult?.plantsFormStates?.[0];
                f.errors = plantValidation.fieldStatus.map(fs => {
                    hasErrors = hasErrors || fs.status === 'error';
                    return {
                        field: fs.field,
                        label: fs.label,
                        statusMessage: fs.statusMessage,
                        status: fs.status
                    };
                });
                return;
            }

            const fieldStatus = validateInitialDataResult?.materialFormState?.fieldStatus?.filter(x => x.tabKey === f.key);
            if (!ObjectsUtils.isArrayEmpty(fieldStatus)) {
                f.errors = fieldStatus
                    .map(fs => {
                        return {
                            field: fs.field,
                            label: fs.label,
                            statusMessage: fs.statusMessage,
                            status: fs.status
                        };
                    });
                hasErrors = hasErrors || fieldStatus?.some(fs => fs.status === 'error');
                hasWarnings = hasWarnings || fieldStatus?.some(fs => fs.status === 'warning');
            }
        });

        return {
            dynamicFormGroup,
            hasErrors,
            hasWarnings,
            genericMessage: {
                messageBody: hasErrors ? 'smartCreation.validation.checkFields' : 'smartCreation.validation.checkWarnings',
                severity: hasErrors ? MessageSeverityType.ERROR : MessageSeverityType.WARNING
            }
        };
    }

    public patchFormControl(formGroup: FormGroup, ctrl: SmartFieldConfiguration, viewMode: ViewModeEnum) {
        formGroup?.enable({onlySelf: true, emitEvent: false});
        const formControl: SmartCreationFormControl = ObjectsUtils.forceCast<SmartCreationFormControl>(formGroup.controls?.[ctrl.id]);

        this.setFormControlStatus(viewMode, ctrl, formControl);

        formControl?.patchValue(this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl.value) : ctrl.value);

        if (ctrl.unitsOfMeasure && ctrl.unitsOfMeasure.length > 0) {
            let uomControl: SmartCreationFormControl = ObjectsUtils.forceCast<SmartCreationFormControl>(formGroup.controls?.[ctrl.id + '.mu']);

            if (ObjectsUtils.isNoU(uomControl)) {
                uomControl = new SmartCreationFormControl(
                    ctrl.unitsOfMeasureSelected ?? ctrl.unitsOfMeasure[0],
                    [],
                    null,
                    null,
                    ctrl.unitsOfMeasure,
                    null
                );
            }

            this.setFormControlStatus(viewMode, ctrl, uomControl);
            formGroup.addControl(
                ctrl.id + '.mu',
                uomControl
            );
        }
    }

    private setFormControlStatus(viewMode: ViewModeEnum,
                                 smartFieldConfigurationNew: SmartFieldConfiguration,
                                 control: SmartCreationFormControl<any>) {
        if (viewMode === ViewModeEnum.DETAILS || viewMode === ViewModeEnum.SUMMARY || viewMode === ViewModeEnum.GR_EDIT_DETAILS || viewMode === ViewModeEnum.PROCESS_DETAILS_VIEW || viewMode === ViewModeEnum.PROCESS_DETAILS_EDIT || !smartFieldConfigurationNew.editable) {
            control.disable({onlySelf: true, emitEvent: false});
            control.clearValidators();
        } else {
            control.enable({onlySelf: true, emitEvent: false});
            control.clearValidators();
            if (smartFieldConfigurationNew.mandatory) {
                control.addValidators(Validators.required);
            }
            if (smartFieldConfigurationNew.length > 0) {
                control.addValidators(Validators.maxLength(smartFieldConfigurationNew.length));
            }
        }
    }


    private initFormControl(ctrl: SmartFieldConfiguration): SmartCreationFormControl {
        const value = this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl?.value) : ctrl?.value;
        return new SmartCreationFormControl(value,
            ctrl.mandatory ? [Validators.required] : [],
            null,
            ctrl.label,
            ctrl.dropdownValues && ctrl.dropdownValues.length > 0 ? ctrl.dropdownValues : null,
            'smartCreation.smartValidation.error'
        );
    }

    private needArrayConversion(cfg: any) {
        return needFlatArray(cfg);
    }

    private componentBasedOnType(v: SmartFieldConfiguration,
                                 page: string,
                                 formGroup: FormGroup,
                                 sheetIndex: number,
                                 tabIndex: number,
                                 dynamicAutocompleteFn?,
                                 initialData?: SmartCreationMaterialDetail,
                                 updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void,
                                 viewMode?: ViewModeEnum): DynamicComponentWrapper {

        return this.dynamicFormInputFactoryService.buildDynamicFormInput(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
    }

    checkPlants(plants: SmartPlantData[], newPlant: SmartPlantData, dfg: DynamicFormGroup): string {
        // const dfg = this.dynamicFormData();
        const formGroup = dfg?.formGroup;
        const plantFormControl = formGroup?.controls[TAm4StandardFieldNames.PLANT] as SmartCreationFormControl;
        const plantComponent = dfg?.items.find(c => c.id === TAm4StandardFieldNames.PLANT);

        if (formGroup?.invalid) {
            return null;
        }

        const currPlantKey = plantFormControl?.value[0];


        for (const plant of plants) {
            const cPlantVal = plant?.attributes?.find(a => a.id === TAm4StandardFieldNames.PLANT)?.value;
            if (cPlantVal === currPlantKey) {
                const errors = {'smartCreation.smartCreation.plantData.errors.plant-duplicates': true};

                plantFormControl.updateValueAndValidity();
                plantFormControl.setErrors(errors);
                plantComponent.hasError = true;
                plantFormControl.validateErrors = [
                    {
                        field: TAm4StandardFieldNames.PLANT,
                        statusMessage: 'smartCreation.smartCreation.plantData.errors.plant-duplicates',
                        status: 'error'
                    }
                ];
                return 'smartCreation.smartCreation.plantData.errors.plant-duplicates';
            }
        }

        return null;
    }


}


