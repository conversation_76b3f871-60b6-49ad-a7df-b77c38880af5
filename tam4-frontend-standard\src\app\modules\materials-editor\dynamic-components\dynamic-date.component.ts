import {Component} from '@angular/core';
import {DynamicBaseInputComponent} from './dynamic-baseinput.component';
import {BehaviorSubject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {ObjectsUtils} from "src/app/utils";


@Component({
  selector: 'dynamic-input-date',
  template: `
    @if(formControl) {
    <p-calendar [ngModel]="currValue | async"
                [dateFormat]="dateFormat"
                showIcon="true"
                showClear="true"
                (onSelect)="onValueChange($event)"
                (onClear)="onValueChange($event)"
                [placeholder]="'smartCreation.smartValidation.date-placeholder' | translate"
                [dataType]="dataType"
                (onBlur)="this.onFocusout($event)"
    >
    </p-calendar>
    }
  `,
  // changeDetection: ChangeDetectionStrategy.OnPush
})

export class DynamicDateComponent extends DynamicBaseInputComponent {


  currValue: BehaviorSubject<any> = new BehaviorSubject<any>(null);


  ngOnInit() {
    super.ngOnInit();
    this.currValue.next(this.getCurrValue());

    this.formGroup?.valueChanges.pipe(takeUntil(this.ngOnDestroy$)).subscribe(v => {
      this.currValue.next(this.getCurrValue());
    })
  }

  getCurrValue() {

    if (ObjectsUtils.isNoU(this.formControl.value || this.componentProps?.value)) {
      return null;
    }

    if (this.dataType === 'date') {
      return new Date(Number(this.formControl.value || this.componentProps?.value));
    }
    return this.formControl.value
  }

  onValueChange($event: Date) {
    this.formControl.setValue($event.getTime())
  }

  onFocusout($event: any) {

    super.onFocusOut($event);
  }
}
