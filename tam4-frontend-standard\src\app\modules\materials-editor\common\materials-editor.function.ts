import {
    BaseMaterialEditorTabsKeys,
    SmartItemTab,
    ValidateDataResponse,
    ViewModeEnum
} from '../models/material-editor.types';
import {CommonResponseWrapper, TAm4StandardFieldNames} from '../../../models';
import {
    GoldenRecordDetails,
    SmartCreationMaterialDetail,
    SmartFieldConfiguration,
    SmartPlantData
} from "../../smart-creation/models/smart-creation.types";
import {ObjectsUtils} from 'src/app/utils';
import {
    DocumentData,
    DynamicDescriptionComponent,
    DynamicInputComponentProps
} from "src/app/modules/materials-editor/dynamic-components";
import {FormGroup} from "@angular/forms";
import {CategoryKey} from "@creactives/models";
import { SelectedMaterials } from '../../search/store/search.state';
import { SearchMaterial } from '../../search/endpoint/search.model';

const PLANT_ATTRIBUTES_TYPES = [
    "plant",
    "plant_key"
]

const PLANT_TABLES_ATTRIBUTES = [
    "4_TAM_MaterialValuation",
    "4_TAM_PlantPlanningInformation",
    "4_TAM_StorageLocations",
    "4_TAM_PlantTotalValues",
];

export function initPlantSheet(material: SmartCreationMaterialDetail): SmartItemTab {
    const plantSheet = material.materialSheets?.filter(m => m.tabKey === BaseMaterialEditorTabsKeys.PLANT_DATA) ?
        material.materialSheets?.filter(m => m.tabKey === BaseMaterialEditorTabsKeys.PLANT_DATA)[0] : null;
    return {
        ...plantSheet,
        errors: []
    };
}

export function parseValidateResponse(payload: CommonResponseWrapper<any>): ValidateDataResponse {
    let data = payload?.data as ValidateDataResponse;
    if (!data) {
        data = {messages: payload?.messages, materialFormState: null, plantsFormStates: null, outcome: null};
    }
    data.messages = payload?.messages;
    data.outcome = payload?.outcome;
    data?.plantsFormStates?.forEach(plant => {
        if (plant.fieldStatus) {
            data.materialFormState.fieldStatus.push(...plant.fieldStatus);
        }
        if (plant.tabStatuses) {
            data.materialFormState.tabStatuses.push(...plant.tabStatuses);
        }
    });
    return data;
}


export function flattenPlantsDetails(plants: SmartPlantData[]) {
    return plants?.map(p => {
        return {
            ...p,
            attributes: p.attributes?.map(a => {
                if (ObjectsUtils.isNoU(a)) {
                    return a;
                } else {
                    return {
                        ...a,
                        value: Array.isArray(a.value) ? a.value[0] : a.value?.key ?? a.value
                    };
                }
            })
        };
    });
}

export function addPlantId(plants: SmartPlantData[], clientId?: string): SmartPlantData[] {
    return plants.filter(p => ObjectsUtils.isNotNoU(p))?.map(p => {
        return {
            ...p,
            id: extractPlantKey(p?.attributes, clientId)
        };
    });
}

export function extractPlantKey(attributes: SmartFieldConfiguration[], clientId?: string) {
    return `${clientId || 'not-defined'}/${attributes.find(a => a.id === TAm4StandardFieldNames.PLANT)?.value}`;
}

export function flattenMaterialFormControlList(materialDetails: SmartCreationMaterialDetail) {
    const materialDetailsRequests: SmartFieldConfiguration[] = [];
    materialDetails?.materialSheets?.map(x => {
        materialDetailsRequests.push(...ObjectsUtils.copyArrayWithoutPropertyAndNullValues(
            x.requests?.filter(attribute => !PLANT_ATTRIBUTES_TYPES.includes(attribute.attributeValueType)),
            'dropdownValues'));
    });
    return materialDetailsRequests;
}

export function flattenPlantFormControlList(materialDetails: SmartCreationMaterialDetail, plantForm: FormGroup) {
    const materialDetailsRequests: SmartFieldConfiguration[] = [];
    materialDetails?.materialSheets?.map(x => {
        materialDetailsRequests.push(...ObjectsUtils.copyArrayWithoutPropertyAndNullValues(    x.requests?.filter(attribute => PLANT_ATTRIBUTES_TYPES.includes(attribute.attributeValueType) && !PLANT_TABLES_ATTRIBUTES.includes(attribute.attributeName)),
            'dropdownValues'));
    });

    return materialDetailsRequests;
}

export function flattenPlantGrInstancesFormControlList(plantSheetGrDetails: any, plantForm: FormGroup) {
    const plantsGrInstances: SmartFieldConfiguration[] = [];
    plantSheetGrDetails?.map(x => {
        plantsGrInstances.push(...ObjectsUtils.copyArrayWithoutPropertyAndNullValues(
            x.requests?.filter(attribute => PLANT_ATTRIBUTES_TYPES.includes(attribute.attributeValueType) && !PLANT_TABLES_ATTRIBUTES.includes(attribute.attributeName)),
            'dropdownValues'));
    });
    return plantsGrInstances;
}

export function addUnitOfMeasureFields(input: SmartItemTab[]): DocumentData {
    const output: DocumentData = {};
    if (input) {
        input.forEach(tab => {
            tab.requests?.forEach(field => {
                if (field) {
                    output[field.id] = field.value;
                    if (field.unitsOfMeasureSelected) {
                        output[field.id + '_UM'] = field.unitsOfMeasureSelected;
                    }
                }

            });

        });
    }
    return output;
}

export function buildDescription(isTextArea: boolean,
                                 page: string,
                                 v: SmartFieldConfiguration,
                                 formGroup: FormGroup<any>): { component: any, componentParams: any } {
    const componentParams: DynamicInputComponentProps = {
        page,
        id: v.id,
        viewMode: ViewModeEnum.CREATE,
        componentProps: {
            editable: v.editable,
            value: v.value,
            relatedAttribute: v.relatedAttribute,
            actionType: ObjectsUtils.isNoU(formGroup) ? 'clipboard' : 'input',
            textarea: isTextArea
        },
        formControlName: v.id,
        formGroup,
        mandatory: v.mandatory,
    } as any;
    return {component: DynamicDescriptionComponent, componentParams: componentParams};
}

export function extractGoldenRecordDetails(materialDetails: SmartCreationMaterialDetail): GoldenRecordDetails | null {
    const goldenRecordSheet = materialDetails?.materialSheets?.find(x => x.tabKey === BaseMaterialEditorTabsKeys.GOLDEN_RECORD);
    if (!goldenRecordSheet) {
        return null;
    }

    const goldenRecordDetails = {...goldenRecordSheet.goldenRecordDetails};
    goldenRecordDetails.instances = goldenRecordDetails.instances?.map(instance => {
        const newInstance = {...instance};
        if (instance.plant) {
            newInstance.plant = instance.plant.map(a => {
                let newValue = ObjectsUtils.isNotNoU(a.value) ? (Array.isArray(a.value) ? a.value[0] : a.value?.key ?? a.value) : null;
                return {...a, value: newValue};
            });
        }
        return newInstance;
    });
    return goldenRecordDetails;
}

export function flattenCategories(categoriesMapWithHistory: { [key: string]: CategoryKey[] }) {
    const flatCategoriesMap: { [key: string]: CategoryKey } = {};
    if (categoriesMapWithHistory) {
        Object.keys(categoriesMapWithHistory).forEach(key => {
            const categoryArray = categoriesMapWithHistory[key];
            if (categoryArray && categoryArray.length > 0) {
                flatCategoriesMap[key] = categoryArray[0];
            }
        });
    }
    return flatCategoriesMap;
}


export function convertInstancesToLink(materials: Array<SearchMaterial>): SelectedMaterials[] {
    return materials.map(material => ({
      materialId: material.masterUuid ?? material.uuid,
      clientId: material.client,
      goldenRecord: material.goldenRecord,
      goldenRecordCode: material.goldenRecordCode,
      description: material.description,
      materialCode: material.materialCode
    }));
  }

export function setUseTranslatePipe(fieldName: string): string {
    switch (fieldName) {
        case '4_SDM_Plant':
            return 'plant';
        case '4_SDM_Client':
            return 'client';
        default:
            return null;
    }
}


export function normalizeControlsFormGroup(formGroup: FormGroup<any>, sheets: SmartFieldConfiguration[]) {
  Object.keys(formGroup.controls).forEach(key => {
    const baseKey = key.replace('.mu', '');
    const existsInSheet = sheets.some(k => k.id === baseKey);
    if (!existsInSheet) {
        formGroup.removeControl(key);
    }
  });
}
